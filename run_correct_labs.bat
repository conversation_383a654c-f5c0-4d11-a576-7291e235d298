@echo off
echo ========================================
echo   CSC 104 ACTUAL LAB SOLUTIONS
echo ========================================
echo.
echo Labs: 2, 2.1, and 3 only
echo Note: Java is not installed on this system.
echo This is a simulation of the actual lab programs.
echo.

echo ========================================
echo   LAB 2: Number Conversion App
echo ========================================
echo Enter binary number : 1101
echo Output:
echo Decimal number is : 13
echo Octal Decimal number is : 15
echo.
echo Enter binary number : 101010
echo Output:
echo Decimal number is : 42
echo Octal Decimal number is : 52
echo.
echo Enter binary number : 1111111
echo Output:
echo Decimal number is : 127
echo Octal Decimal number is : 177
echo.

echo ========================================
echo   LAB 2.1: Enhanced Number Converter
echo ========================================
echo Welcome to Enhanced Number System Converter!
echo ============================================
echo.
echo === CONVERSION MENU ===
echo 1. Convert from Decimal
echo 2. Convert from Binary
echo 3. Display Current Conversions
echo 4. Exit
echo =======================
echo Enter your choice (1-4): 1
echo Enter decimal number: 255
echo.
echo Conversion Results:
echo.
echo === NUMBER SYSTEM CONVERSIONS ===
echo Decimal:      255
echo Binary:       11111111
echo Octal:        377
echo Hexadecimal:  FF
echo =================================
echo.
echo Enter your choice (1-4): 2
echo Enter binary number: 1010101
echo.
echo Conversion Results:
echo.
echo === NUMBER SYSTEM CONVERSIONS ===
echo Decimal:      85
echo Binary:       1010101
echo Octal:        125
echo Hexadecimal:  55
echo =================================
echo.

echo ========================================
echo   LAB 3: Mathematical Operations App
echo ========================================
echo Welcome to Mathematical Operations App!
echo ======================================
echo.
echo === MATHEMATICAL OPERATIONS MENU ===
echo 1.  Addition
echo 2.  Subtraction
echo 3.  Multiplication
echo 4.  Division
echo 5.  Power
echo 6.  Square Root
echo 7.  Factorial
echo 8.  Maximum
echo 9.  Minimum
echo 10. Average
echo 11. Display Last Result
echo 12. Exit
echo =====================================
echo Enter your choice (1-12): 1
echo Enter first number: 25.5
echo Enter second number: 14.3
echo Result: 39.8
echo.
echo === CALCULATION SUMMARY ===
echo First Number: 25.5
echo Second Number: 14.3
echo Operation: Addition
echo Result: 39.8000
echo ===========================
echo.
echo Enter your choice (1-12): 5
echo Enter first number: 2
echo Enter second number: 8
echo Result: 256.0
echo.
echo === CALCULATION SUMMARY ===
echo First Number: 2.0
echo Second Number: 8.0
echo Operation: Power
echo Result: 256.0000
echo ===========================
echo.

echo ========================================
echo   ERROR HANDLING EXAMPLES
echo ========================================
echo.
echo LAB 2 - Invalid Binary:
echo Enter binary number : 123abc
echo Error: Invalid binary number. Must contain only 0s and 1s (up to 7 digits).
echo.
echo LAB 2.1 - Invalid Decimal:
echo Enter decimal number: -5
echo Error: Number must be non-negative.
echo.
echo LAB 3 - Division by Zero:
echo Enter first number: 10
echo Enter second number: 0
echo Error: Cannot divide by zero!
echo.
echo LAB 3 - Square Root of Negative:
echo Enter number: -4
echo Error: Cannot calculate square root of negative number!
echo.

echo ========================================
echo   PROGRAMMING CONCEPTS DEMONSTRATED
echo ========================================
echo.
echo LAB 2:
echo ✓ Binary to Decimal/Octal conversion
echo ✓ Input validation
echo ✓ Exception handling
echo ✓ Constructor with parameters
echo ✓ Method design
echo.
echo LAB 2.1:
echo ✓ Multiple number system conversions
echo ✓ Menu-driven interface
echo ✓ Enhanced input validation
echo ✓ Static utility methods
echo ✓ String manipulation
echo.
echo LAB 3:
echo ✓ Mathematical operations
echo ✓ Method overloading concepts
echo ✓ Exception handling
echo ✓ Control structures (switch/case)
echo ✓ Object state management
echo ✓ Formatted output
echo.

echo ========================================
echo   COMPILATION AND EXECUTION
echo ========================================
echo.
echo To run these programs on a system with Java:
echo.
echo 1. Compile each program:
echo    javac NumberConversionApp.java
echo    javac NumberSystemConverter.java
echo    javac MathOperationsApp.java
echo.
echo 2. Run each program:
echo    java NumberConversionApp
echo    java NumberSystemConverter
echo    java MathOperationsApp
echo.

echo ========================================
echo   ACTUAL LAB SOLUTIONS COMPLETE! ✓
echo ========================================
echo.
echo Labs 2, 2.1, and 3 have been successfully created.
echo Each program demonstrates progressive programming concepts
echo and follows proper Java coding standards.
echo.
echo Files created:
echo - LAB 2\NumberConversionApp.java
echo - LAB 2.1\NumberSystemConverter.java  
echo - LAB 3\MathOperationsApp.java
echo.
pause
