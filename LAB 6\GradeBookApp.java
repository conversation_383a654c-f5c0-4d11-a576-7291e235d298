import java.util.Scanner;
import java.util.ArrayList;

/**
 * GradeBookApp - Student grade management system
 * CSC 104 Programming Activity #6
 * Demonstrates object-oriented programming and collections
 */
public class GradeBookApp {
    // Inner class to represent a student
    static class Student {
        private String name;
        private int id;
        private ArrayList<Double> grades;
        
        public Student(String name, int id) {
            this.name = name;
            this.id = id;
            this.grades = new ArrayList<>();
        }
        
        public void addGrade(double grade) {
            if (grade >= 0 && grade <= 100) {
                grades.add(grade);
            } else {
                throw new IllegalArgumentException("Grade must be between 0 and 100");
            }
        }
        
        public double calculateAverage() {
            if (grades.isEmpty()) return 0.0;
            double sum = 0;
            for (double grade : grades) {
                sum += grade;
            }
            return sum / grades.size();
        }
        
        public char getLetterGrade() {
            double avg = calculateAverage();
            if (avg >= 90) return 'A';
            else if (avg >= 80) return 'B';
            else if (avg >= 70) return 'C';
            else if (avg >= 60) return 'D';
            else return 'F';
        }
        
        public String getName() { return name; }
        public int getId() { return id; }
        public ArrayList<Double> getGrades() { return new ArrayList<>(grades); }
        
        @Override
        public String toString() {
            return String.format("ID: %d, Name: %s, Average: %.2f, Letter Grade: %c", 
                               id, name, calculateAverage(), getLetterGrade());
        }
    }
    
    // Instance variables
    private ArrayList<Student> students;
    private String courseName;
    
    /**
     * Constructor
     * @param courseName name of the course
     */
    public GradeBookApp(String courseName) {
        this.courseName = courseName;
        this.students = new ArrayList<>();
    }
    
    /**
     * Adds a new student
     * @param name student name
     * @param id student ID
     */
    public void addStudent(String name, int id) {
        // Check if ID already exists
        for (Student student : students) {
            if (student.getId() == id) {
                throw new IllegalArgumentException("Student ID already exists!");
            }
        }
        students.add(new Student(name, id));
    }
    
    /**
     * Finds a student by ID
     * @param id student ID
     * @return Student object or null if not found
     */
    public Student findStudent(int id) {
        for (Student student : students) {
            if (student.getId() == id) {
                return student;
            }
        }
        return null;
    }
    
    /**
     * Adds a grade to a student
     * @param studentId student ID
     * @param grade grade to add
     */
    public void addGrade(int studentId, double grade) {
        Student student = findStudent(studentId);
        if (student != null) {
            student.addGrade(grade);
        } else {
            throw new IllegalArgumentException("Student not found!");
        }
    }
    
    /**
     * Displays all students
     */
    public void displayAllStudents() {
        System.out.println("\n=== " + courseName + " Grade Book ===");
        if (students.isEmpty()) {
            System.out.println("No students enrolled.");
        } else {
            for (Student student : students) {
                System.out.println(student);
            }
        }
        System.out.println("================================");
    }
    
    /**
     * Displays detailed information for a specific student
     * @param studentId student ID
     */
    public void displayStudentDetails(int studentId) {
        Student student = findStudent(studentId);
        if (student != null) {
            System.out.println("\n=== Student Details ===");
            System.out.println("Name: " + student.getName());
            System.out.println("ID: " + student.getId());
            System.out.println("Grades: " + student.getGrades());
            System.out.println("Average: " + String.format("%.2f", student.calculateAverage()));
            System.out.println("Letter Grade: " + student.getLetterGrade());
            System.out.println("=======================");
        } else {
            System.out.println("Student not found!");
        }
    }
    
    /**
     * Calculates class average
     * @return class average
     */
    public double calculateClassAverage() {
        if (students.isEmpty()) return 0.0;
        double sum = 0;
        for (Student student : students) {
            sum += student.calculateAverage();
        }
        return sum / students.size();
    }
    
    /**
     * Finds the highest performing student
     * @return Student with highest average
     */
    public Student findTopStudent() {
        if (students.isEmpty()) return null;
        Student topStudent = students.get(0);
        for (Student student : students) {
            if (student.calculateAverage() > topStudent.calculateAverage()) {
                topStudent = student;
            }
        }
        return topStudent;
    }
    
    /**
     * Counts students by letter grade
     */
    public void displayGradeDistribution() {
        int[] gradeCounts = new int[5]; // A, B, C, D, F
        
        for (Student student : students) {
            char grade = student.getLetterGrade();
            switch (grade) {
                case 'A': gradeCounts[0]++; break;
                case 'B': gradeCounts[1]++; break;
                case 'C': gradeCounts[2]++; break;
                case 'D': gradeCounts[3]++; break;
                case 'F': gradeCounts[4]++; break;
            }
        }
        
        System.out.println("\n=== Grade Distribution ===");
        System.out.println("A: " + gradeCounts[0] + " students");
        System.out.println("B: " + gradeCounts[1] + " students");
        System.out.println("C: " + gradeCounts[2] + " students");
        System.out.println("D: " + gradeCounts[3] + " students");
        System.out.println("F: " + gradeCounts[4] + " students");
        System.out.println("==========================");
    }
    
    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            System.out.print("Enter course name: ");
            String courseName = scanner.nextLine();
            GradeBookApp gradeBook = new GradeBookApp(courseName);
            
            System.out.println("Welcome to " + courseName + " Grade Book!");
            
            int choice;
            do {
                System.out.println("\n=== GRADE BOOK MENU ===");
                System.out.println("1. Add Student");
                System.out.println("2. Add Grade");
                System.out.println("3. Display All Students");
                System.out.println("4. Display Student Details");
                System.out.println("5. Show Class Statistics");
                System.out.println("6. Show Grade Distribution");
                System.out.println("7. Exit");
                System.out.println("=======================");
                System.out.print("Enter your choice (1-7): ");
                choice = scanner.nextInt();
                scanner.nextLine(); // Consume newline
                
                switch (choice) {
                    case 1:
                        try {
                            System.out.print("Enter student name: ");
                            String name = scanner.nextLine();
                            System.out.print("Enter student ID: ");
                            int id = scanner.nextInt();
                            gradeBook.addStudent(name, id);
                            System.out.println("Student added successfully!");
                        } catch (Exception e) {
                            System.err.println("Error: " + e.getMessage());
                        }
                        break;
                        
                    case 2:
                        try {
                            System.out.print("Enter student ID: ");
                            int studentId = scanner.nextInt();
                            System.out.print("Enter grade (0-100): ");
                            double grade = scanner.nextDouble();
                            gradeBook.addGrade(studentId, grade);
                            System.out.println("Grade added successfully!");
                        } catch (Exception e) {
                            System.err.println("Error: " + e.getMessage());
                        }
                        break;
                        
                    case 3:
                        gradeBook.displayAllStudents();
                        break;
                        
                    case 4:
                        System.out.print("Enter student ID: ");
                        int detailId = scanner.nextInt();
                        gradeBook.displayStudentDetails(detailId);
                        break;
                        
                    case 5:
                        System.out.println("\n=== Class Statistics ===");
                        System.out.println("Total Students: " + gradeBook.students.size());
                        System.out.println("Class Average: " + 
                                         String.format("%.2f", gradeBook.calculateClassAverage()));
                        Student topStudent = gradeBook.findTopStudent();
                        if (topStudent != null) {
                            System.out.println("Top Student: " + topStudent.getName() + 
                                             " (Average: " + String.format("%.2f", topStudent.calculateAverage()) + ")");
                        }
                        System.out.println("========================");
                        break;
                        
                    case 6:
                        gradeBook.displayGradeDistribution();
                        break;
                        
                    case 7:
                        System.out.println("Thank you for using Grade Book App!");
                        break;
                        
                    default:
                        System.out.println("Invalid choice! Please enter 1-7.");
                }
                
            } while (choice != 7);
            
        } catch (Exception e) {
            System.err.println("Error: Invalid input.");
        }
    }
}
