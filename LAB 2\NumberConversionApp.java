import java.util.Scanner;

/**
 * NumberConversionApp - converts binary numbers to decimal and octal
 * CSC 104 Programming Activity #2
 */
public class NumberConversionApp {
// Instance variables
        private int decNumber;

    /**
     * Constructor with two parameters
     * @param x dummy parameter (unused)
     * @param y dummy parameter (unused)
     */
    public NumberConversionApp(int x, int y) {
        this.decNumber = 0;
    }

    /**
     * Sets the binary number and performs conversions
     * @param binNumber the binary number to convert (up to 7 digits)
     */
    public void setBinNumber(String binNumber) {
        // Validate binary input
        if (!binNumber.matches("[01]{1,7}")) {
            throw new IllegalArgumentException("Invalid binary number. Must contain only 0s and 1s (up to 7 digits).");
        }
        
        this.decNumber = getDecNumber(Integer.parseInt(binNumber, 2));
        getOctNumber(this.decNumber);
    }

    /**
     * Converts decimal number to decimal (identity function in this case)
     * @param x decimal number (already converted from binary)
     * @return the same decimal number
     */
    public int getDecNumber(int x) {
        return x;
    }

    /**
     * Converts decimal number to octal string
     * @param x decimal number to convert
     * @return octal representation as string
     */
    public String getOctNumber(int x) {
        return Integer.toOctalString(x);
    }

    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            // Create instance with constructor (parameters are dummy values)
            NumberConversionApp converter = new NumberConversionApp(0, 0);
            
            // Get user input
            System.out.print("Enter binary number : ");
            String binaryInput = scanner.nextLine();
            
            // Perform conversion
            converter.setBinNumber(binaryInput);
            
            // Display results in the exact required format
            System.out.println("Output:");
            System.out.println("Decimal number is : " + converter.getDecNumber(converter.decNumber));
            System.out.println("Octal Decimal number is : " + converter.getOctNumber(converter.decNumber));
            
        } catch (IllegalArgumentException e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
}