
/**
 * NumberConversionApp - converts binary numbers to decimal and octal
 * CSC 104 Programming Activity #2
 */
public class NumberConversionApp {
    private int decNumber;
    
    public NumberConversionApp(int x, int y) {
        this.decNumber = 0;
    }
    
    public int getDecNumber() {
        return this.decNumber;
    }

    public String getOctNumber(int x) {
        return Integer.toOctalString(x);
    }
}



