import java.util.Scanner;

/**
 * NumberSystemConverter - Enhanced number system conversion
 * CSC 104 Programming Activity #2.1
 * Extended version of Lab 2 with additional conversion features
 */
public class NumberSystemConverter {
    // Instance variables
    private int decimalNumber;
    private String binaryNumber;
    private String octalNumber;
    private String hexadecimalNumber;
    
    /**
     * Constructor with two parameters (as required)
     * @param x dummy parameter (unused)
     * @param y dummy parameter (unused)
     */
    public NumberSystemConverter(int x, int y) {
        this.decimalNumber = 0;
        this.binaryNumber = "";
        this.octalNumber = "";
        this.hexadecimalNumber = "";
    }
    
    /**
     * Sets the decimal number and performs all conversions
     * @param decimal the decimal number to convert
     */
    public void setDecimalNumber(int decimal) {
        if (decimal < 0) {
            throw new IllegalArgumentException("Number must be non-negative.");
        }
        
        this.decimalNumber = decimal;
        this.binaryNumber = getBinaryNumber(decimal);
        this.octalNumber = getOctalNumber(decimal);
        this.hexadecimalNumber = getHexadecimalNumber(decimal);
    }
    
    /**
     * Sets the binary number and converts to other systems
     * @param binary the binary number as string
     */
    public void setBinaryNumber(String binary) {
        // Validate binary input
        if (!binary.matches("[01]+")) {
            throw new IllegalArgumentException("Invalid binary number. Must contain only 0s and 1s.");
        }
        
        this.binaryNumber = binary;
        this.decimalNumber = Integer.parseInt(binary, 2);
        this.octalNumber = getOctalNumber(this.decimalNumber);
        this.hexadecimalNumber = getHexadecimalNumber(this.decimalNumber);
    }
    
    /**
     * Converts decimal to binary
     * @param decimal decimal number
     * @return binary representation as string
     */
    public String getBinaryNumber(int decimal) {
        if (decimal == 0) return "0";
        
        StringBuilder binary = new StringBuilder();
        while (decimal > 0) {
            binary.insert(0, decimal % 2);
            decimal /= 2;
        }
        return binary.toString();
    }
    
    /**
     * Converts decimal to octal
     * @param decimal decimal number
     * @return octal representation as string
     */
    public String getOctalNumber(int decimal) {
        return Integer.toOctalString(decimal);
    }
    
    /**
     * Converts decimal to hexadecimal
     * @param decimal decimal number
     * @return hexadecimal representation as string
     */
    public String getHexadecimalNumber(int decimal) {
        return Integer.toHexString(decimal).toUpperCase();
    }
    
    /**
     * Gets the stored decimal number
     * @return decimal number
     */
    public int getDecimalNumber() {
        return this.decimalNumber;
    }
    
    /**
     * Gets the stored binary number
     * @return binary number as string
     */
    public String getStoredBinaryNumber() {
        return this.binaryNumber;
    }
    
    /**
     * Gets the stored octal number
     * @return octal number as string
     */
    public String getStoredOctalNumber() {
        return this.octalNumber;
    }
    
    /**
     * Gets the stored hexadecimal number
     * @return hexadecimal number as string
     */
    public String getStoredHexadecimalNumber() {
        return this.hexadecimalNumber;
    }
    
    /**
     * Displays all number system representations
     */
    public void displayAllConversions() {
        System.out.println("\n=== NUMBER SYSTEM CONVERSIONS ===");
        System.out.println("Decimal:      " + this.decimalNumber);
        System.out.println("Binary:       " + this.binaryNumber);
        System.out.println("Octal:        " + this.octalNumber);
        System.out.println("Hexadecimal:  " + this.hexadecimalNumber);
        System.out.println("=================================");
    }
    
    /**
     * Validates if a string is a valid decimal number
     * @param str string to validate
     * @return true if valid decimal, false otherwise
     */
    public static boolean isValidDecimal(String str) {
        try {
            int num = Integer.parseInt(str);
            return num >= 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * Validates if a string is a valid binary number
     * @param str string to validate
     * @return true if valid binary, false otherwise
     */
    public static boolean isValidBinary(String str) {
        return str.matches("[01]+") && !str.isEmpty();
    }
    
    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            // Create instance with constructor (parameters are dummy values)
            NumberSystemConverter converter = new NumberSystemConverter(0, 0);
            
            System.out.println("Welcome to Enhanced Number System Converter!");
            System.out.println("============================================");
            
            int choice;
            do {
                System.out.println("\n=== CONVERSION MENU ===");
                System.out.println("1. Convert from Decimal");
                System.out.println("2. Convert from Binary");
                System.out.println("3. Display Current Conversions");
                System.out.println("4. Exit");
                System.out.println("=======================");
                System.out.print("Enter your choice (1-4): ");
                
                choice = scanner.nextInt();
                scanner.nextLine(); // Consume newline
                
                switch (choice) {
                    case 1:
                        System.out.print("Enter decimal number: ");
                        String decInput = scanner.nextLine();
                        
                        if (isValidDecimal(decInput)) {
                            int decimal = Integer.parseInt(decInput);
                            converter.setDecimalNumber(decimal);
                            
                            System.out.println("\nConversion Results:");
                            converter.displayAllConversions();
                        } else {
                            System.err.println("Error: Invalid decimal number. Must be a non-negative integer.");
                        }
                        break;
                        
                    case 2:
                        System.out.print("Enter binary number: ");
                        String binInput = scanner.nextLine();
                        
                        if (isValidBinary(binInput)) {
                            converter.setBinaryNumber(binInput);
                            
                            System.out.println("\nConversion Results:");
                            converter.displayAllConversions();
                        } else {
                            System.err.println("Error: Invalid binary number. Must contain only 0s and 1s.");
                        }
                        break;
                        
                    case 3:
                        if (converter.getDecimalNumber() == 0 && converter.getStoredBinaryNumber().isEmpty()) {
                            System.out.println("No number has been entered yet. Please convert a number first.");
                        } else {
                            converter.displayAllConversions();
                        }
                        break;
                        
                    case 4:
                        System.out.println("Thank you for using Number System Converter!");
                        break;
                        
                    default:
                        System.out.println("Invalid choice! Please enter 1-4.");
                }
                
            } while (choice != 4);
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
}
