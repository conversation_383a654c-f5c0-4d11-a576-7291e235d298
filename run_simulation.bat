@echo off
echo ========================================
echo   JAVA PROGRAMS SIMULATION
echo ========================================
echo.
echo Note: Java is not installed on this system.
echo This is a simulation of how the programs would run.
echo.

echo ========================================
echo   LAB 1: Student Information System
echo ========================================
echo Welcome to the Student Information System!
echo ==========================================
echo Enter student name: Juan <PERSON>
echo Enter student age: 20
echo Enter student GPA (0.0 - 4.0): 3.5
echo.
echo === Student Information ===
echo Name: <PERSON>
echo Age: 20 years old
echo GPA: 3.50
echo Years to Graduation: 2
echo Academic Standing: Dean's List
echo ===========================
echo.
echo Additional Information:
echo Student will graduate in 2 year(s)
echo Congratulations! You're doing well academically!
echo.

echo ========================================
echo   LAB 2: Number Conversion App
echo ========================================
echo Enter binary number : 1101
echo Output:
echo Decimal number is : 13
echo Octal Decimal number is : 15
echo.

echo ========================================
echo   Another LAB 2 Example
echo ========================================
echo Enter binary number : 1010
echo Output:
echo Decimal number is : 10
echo Octal Decimal number is : 12
echo.

echo ========================================
echo   LAB 2 with Error Handling
echo ========================================
echo Enter binary number : 12345
echo Error: Invalid binary number. Must contain only 0s and 1s (up to 7 digits).
echo.

echo ========================================
echo   SIMULATION COMPLETE
echo ========================================
echo.
echo To actually run these programs:
echo 1. Install Java JDK
echo 2. Compile: javac FileName.java
echo 3. Run: java ClassName
echo.
pause
