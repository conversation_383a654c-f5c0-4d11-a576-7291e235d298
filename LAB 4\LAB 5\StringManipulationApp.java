import java.util.Scanner;

/**
 * StringManipulationApp - String processing and manipulation
 * CSC 104 Programming Activity #5
 * Demonstrates string methods and character processing
 */
public class StringManipulationApp {
    // Instance variables
    private String inputString;
    
    /**
     * Default constructor
     */
    public StringManipulationApp() {
        this.inputString = "";
    }
    
    /**
     * Constructor with initial string
     * @param str initial string
     */
    public StringManipulationApp(String str) {
        this.inputString = str;
    }
    
    /**
     * Sets the input string
     * @param str string to set
     */
    public void setString(String str) {
        this.inputString = str;
    }
    
    /**
     * Gets the input string
     * @return the input string
     */
    public String getString() {
        return this.inputString;
    }
    
    /**
     * Counts the number of characters
     * @return character count
     */
    public int countCharacters() {
        return inputString.length();
    }
    
    /**
     * Counts the number of words
     * @return word count
     */
    public int countWords() {
        if (inputString.trim().isEmpty()) {
            return 0;
        }
        return inputString.trim().split("\\s+").length;
    }
    
    /**
     * Counts vowels in the string
     * @return vowel count
     */
    public int countVowels() {
        int count = 0;
        String vowels = "aeiouAEIOU";
        for (char c : inputString.toCharArray()) {
            if (vowels.indexOf(c) != -1) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * Counts consonants in the string
     * @return consonant count
     */
    public int countConsonants() {
        int count = 0;
        for (char c : inputString.toCharArray()) {
            if (Character.isLetter(c) && "aeiouAEIOU".indexOf(c) == -1) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * Counts digits in the string
     * @return digit count
     */
    public int countDigits() {
        int count = 0;
        for (char c : inputString.toCharArray()) {
            if (Character.isDigit(c)) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * Reverses the string
     * @return reversed string
     */
    public String reverseString() {
        StringBuilder reversed = new StringBuilder();
        for (int i = inputString.length() - 1; i >= 0; i--) {
            reversed.append(inputString.charAt(i));
        }
        return reversed.toString();
    }
    
    /**
     * Checks if the string is a palindrome
     * @return true if palindrome, false otherwise
     */
    public boolean isPalindrome() {
        String cleaned = inputString.replaceAll("[^a-zA-Z0-9]", "").toLowerCase();
        return cleaned.equals(new StringBuilder(cleaned).reverse().toString());
    }
    
    /**
     * Converts string to uppercase
     * @return uppercase string
     */
    public String toUpperCase() {
        return inputString.toUpperCase();
    }
    
    /**
     * Converts string to lowercase
     * @return lowercase string
     */
    public String toLowerCase() {
        return inputString.toLowerCase();
    }
    
    /**
     * Capitalizes first letter of each word
     * @return title case string
     */
    public String toTitleCase() {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;
        
        for (char c : inputString.toCharArray()) {
            if (Character.isWhitespace(c)) {
                capitalizeNext = true;
                result.append(c);
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }
        return result.toString();
    }
    
    /**
     * Removes all spaces from the string
     * @return string without spaces
     */
    public String removeSpaces() {
        return inputString.replaceAll("\\s", "");
    }
    
    /**
     * Finds the most frequent character
     * @return most frequent character
     */
    public char findMostFrequentChar() {
        if (inputString.isEmpty()) return '\0';
        
        int[] frequency = new int[256]; // ASCII characters
        for (char c : inputString.toCharArray()) {
            frequency[c]++;
        }
        
        char mostFrequent = inputString.charAt(0);
        int maxCount = frequency[mostFrequent];
        
        for (int i = 0; i < frequency.length; i++) {
            if (frequency[i] > maxCount) {
                maxCount = frequency[i];
                mostFrequent = (char) i;
            }
        }
        return mostFrequent;
    }
    
    /**
     * Displays comprehensive string analysis
     */
    public void displayAnalysis() {
        System.out.println("\n=== STRING ANALYSIS ===");
        System.out.println("Original String: \"" + inputString + "\"");
        System.out.println("Length: " + countCharacters() + " characters");
        System.out.println("Words: " + countWords());
        System.out.println("Vowels: " + countVowels());
        System.out.println("Consonants: " + countConsonants());
        System.out.println("Digits: " + countDigits());
        System.out.println("Is Palindrome: " + (isPalindrome() ? "Yes" : "No"));
        System.out.println("Reversed: \"" + reverseString() + "\"");
        System.out.println("Uppercase: \"" + toUpperCase() + "\"");
        System.out.println("Lowercase: \"" + toLowerCase() + "\"");
        System.out.println("Title Case: \"" + toTitleCase() + "\"");
        System.out.println("Without Spaces: \"" + removeSpaces() + "\"");
        
        char mostFreq = findMostFrequentChar();
        if (mostFreq != '\0') {
            System.out.println("Most Frequent Character: '" + mostFreq + "'");
        }
        System.out.println("=======================");
    }
    
    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            StringManipulationApp app = new StringManipulationApp();
            
            System.out.println("Welcome to String Manipulation App!");
            
            int choice;
            do {
                System.out.println("\n=== STRING OPERATIONS MENU ===");
                System.out.println("1. Enter New String");
                System.out.println("2. Display Current String");
                System.out.println("3. Show Complete Analysis");
                System.out.println("4. Count Characters");
                System.out.println("5. Count Words");
                System.out.println("6. Check if Palindrome");
                System.out.println("7. Reverse String");
                System.out.println("8. Convert to Title Case");
                System.out.println("9. Exit");
                System.out.println("===============================");
                System.out.print("Enter your choice (1-9): ");
                choice = scanner.nextInt();
                scanner.nextLine(); // Consume newline
                
                switch (choice) {
                    case 1:
                        System.out.print("Enter a string: ");
                        String newString = scanner.nextLine();
                        app.setString(newString);
                        System.out.println("String updated successfully!");
                        break;
                        
                    case 2:
                        System.out.println("Current string: \"" + app.getString() + "\"");
                        break;
                        
                    case 3:
                        if (app.getString().isEmpty()) {
                            System.out.println("Please enter a string first!");
                        } else {
                            app.displayAnalysis();
                        }
                        break;
                        
                    case 4:
                        System.out.println("Character count: " + app.countCharacters());
                        break;
                        
                    case 5:
                        System.out.println("Word count: " + app.countWords());
                        break;
                        
                    case 6:
                        System.out.println("Is palindrome: " + (app.isPalindrome() ? "Yes" : "No"));
                        break;
                        
                    case 7:
                        System.out.println("Reversed string: \"" + app.reverseString() + "\"");
                        break;
                        
                    case 8:
                        System.out.println("Title case: \"" + app.toTitleCase() + "\"");
                        break;
                        
                    case 9:
                        System.out.println("Thank you for using String Manipulation App!");
                        break;
                        
                    default:
                        System.out.println("Invalid choice! Please enter 1-9.");
                }
                
            } while (choice != 9);
            
        } catch (Exception e) {
            System.err.println("Error: Invalid input.");
        }
    }
}
