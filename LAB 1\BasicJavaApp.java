
import java.util.Scanner;

/**
 * BasicJavaApp - Introduction to Java Programming
 * CSC 104 Programming Activity #1
 */
public class BasicJavaApp {
    private String studentName;
    private int studentAge;
    private double gpa;

    /**
     * Default constructor
     */
    public BasicJavaApp() {
        this.studentName = "";
        this.studentAge = 0;
        this.gpa = 0.0;
    }

    /**
     * Constructor with parameters
     * @param name student's name
     * @param age student's age
     * @param gpa student's GPA
     */
    public BasicJavaApp(String name, int age, double gpa) {
        this.studentName = name;
        this.studentAge = age;
        this.gpa = gpa;
    }

    /**
     * Sets the student information
     * @param name student's name
     * @param age student's age
     * @param gpa student's GPA
     */
    public void setStudentInfo(String name, int age, double gpa) {
        this.studentName = name;
        this.studentAge = age;
        this.gpa = gpa;
    }

    /**
     * Gets the student's name
     * @return student's name
     */
    public String getStudentName() {
        return this.studentName;
    }

    /**
     * Gets the student's age
     * @return student's age
     */
    public int getStudentAge() {
        return this.studentAge;
    }

    /**
     * Gets the student's GPA
     * @return student's GPA
     */
    public double getStudentGPA() {
        return this.gpa;
    }

    /**
     * Calculates years until graduation (assuming 4-year program)
     * @return years remaining
     */
    public int calculateYearsToGraduation() {
        // Assuming freshman = 4 years, sophomore = 3 years, etc.
        if (this.studentAge <= 18) return 4;
        else if (this.studentAge <= 19) return 3;
        else if (this.studentAge <= 20) return 2;
        else if (this.studentAge <= 21) return 1;
        else return 0;
    }

    /**
     * Determines academic standing based on GPA
     * @return academic standing as string
     */
    public String getAcademicStanding() {
        if (this.gpa >= 3.5) return "Dean's List";
        else if (this.gpa >= 3.0) return "Good Standing";
        else if (this.gpa >= 2.0) return "Satisfactory";
        else return "Academic Probation";
    }

    /**
     * Displays student information in formatted output
     */
    public void displayStudentInfo() {
        System.out.println("\n=== Student Information ===");
        System.out.println("Name: " + this.studentName);
        System.out.println("Age: " + this.studentAge + " years old");
        System.out.println("GPA: " + String.format("%.2f", this.gpa));
        System.out.println("Years to Graduation: " + calculateYearsToGraduation());
        System.out.println("Academic Standing: " + getAcademicStanding());
        System.out.println("===========================");
    }

    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            // Create instance of the class
            BasicJavaApp student = new BasicJavaApp();
            
            // Welcome message
            System.out.println("Welcome to the Student Information System!");
            System.out.println("==========================================");

            // Get user input
            System.out.print("Enter student name: ");
            String name = scanner.nextLine();

            System.out.print("Enter student age: ");
            int age = scanner.nextInt();

            System.out.print("Enter student GPA (0.0 - 4.0): ");
            double gpa = scanner.nextDouble();

            // Validate input
            if (age < 0 || age > 150) {
                System.out.println("Warning: Age seems unusual. Please verify.");
            }
            if (gpa < 0.0 || gpa > 4.0) {
                System.out.println("Warning: GPA should be between 0.0 and 4.0");
                return;
            }

            // Set student information
            student.setStudentInfo(name, age, gpa);

            // Display results
            student.displayStudentInfo();

            // Additional calculations
            System.out.println("\nAdditional Information:");
            System.out.println("Student will graduate in " + student.calculateYearsToGraduation() + " year(s)");

            if (student.getStudentGPA() >= 3.0) {
                System.out.println("Congratulations! You're doing well academically!");
            } else {
                System.out.println("Keep working hard to improve your GPA!");
            }

        } catch (Exception e) {
            System.err.println("Error: Invalid input. Please enter valid data.");
            System.err.println("Details: " + e.getMessage());
        }
    }
}




