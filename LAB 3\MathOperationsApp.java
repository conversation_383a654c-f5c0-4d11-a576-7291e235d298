import java.util.Scanner;

/**
 * MathOperationsApp - Mathematical operations and calculations
 * CSC 104 Programming Activity #3
 * Demonstrates methods, mathematical operations, and control structures
 */
public class MathOperationsApp {
    // Instance variables
    private double firstNumber;
    private double secondNumber;
    private String operation;
    private double result;
    
    /**
     * Default constructor
     */
    public MathOperationsApp() {
        this.firstNumber = 0.0;
        this.secondNumber = 0.0;
        this.operation = "";
        this.result = 0.0;
    }
    
    /**
     * Constructor with parameters
     * @param num1 first number
     * @param num2 second number
     */
    public MathOperationsApp(double num1, double num2) {
        this.firstNumber = num1;
        this.secondNumber = num2;
        this.operation = "";
        this.result = 0.0;
    }
    
    /**
     * Sets the numbers for calculation
     * @param num1 first number
     * @param num2 second number
     */
    public void setNumbers(double num1, double num2) {
        this.firstNumber = num1;
        this.secondNumber = num2;
    }
    
    /**
     * Performs addition
     * @return sum of the two numbers
     */
    public double performAddition() {
        this.operation = "Addition";
        this.result = this.firstNumber + this.secondNumber;
        return this.result;
    }
    
    /**
     * Performs subtraction
     * @return difference of the two numbers
     */
    public double performSubtraction() {
        this.operation = "Subtraction";
        this.result = this.firstNumber - this.secondNumber;
        return this.result;
    }
    
    /**
     * Performs multiplication
     * @return product of the two numbers
     */
    public double performMultiplication() {
        this.operation = "Multiplication";
        this.result = this.firstNumber * this.secondNumber;
        return this.result;
    }
    
    /**
     * Performs division
     * @return quotient of the two numbers
     * @throws ArithmeticException if second number is zero
     */
    public double performDivision() throws ArithmeticException {
        if (this.secondNumber == 0) {
            throw new ArithmeticException("Cannot divide by zero!");
        }
        this.operation = "Division";
        this.result = this.firstNumber / this.secondNumber;
        return this.result;
    }
    
    /**
     * Calculates power (first number raised to second number)
     * @return first number raised to the power of second number
     */
    public double performPower() {
        this.operation = "Power";
        this.result = Math.pow(this.firstNumber, this.secondNumber);
        return this.result;
    }
    
    /**
     * Calculates square root of first number
     * @return square root of first number
     * @throws ArithmeticException if first number is negative
     */
    public double performSquareRoot() throws ArithmeticException {
        if (this.firstNumber < 0) {
            throw new ArithmeticException("Cannot calculate square root of negative number!");
        }
        this.operation = "Square Root";
        this.result = Math.sqrt(this.firstNumber);
        return this.result;
    }
    
    /**
     * Calculates factorial of first number (must be non-negative integer)
     * @return factorial of first number
     * @throws ArithmeticException if number is negative or too large
     */
    public long performFactorial() throws ArithmeticException {
        int num = (int) this.firstNumber;
        if (num != this.firstNumber || num < 0) {
            throw new ArithmeticException("Factorial requires a non-negative integer!");
        }
        if (num > 20) {
            throw new ArithmeticException("Number too large for factorial calculation!");
        }
        
        this.operation = "Factorial";
        long factorial = 1;
        for (int i = 1; i <= num; i++) {
            factorial *= i;
        }
        this.result = factorial;
        return factorial;
    }
    
    /**
     * Finds the maximum of the two numbers
     * @return maximum value
     */
    public double findMaximum() {
        this.operation = "Maximum";
        this.result = Math.max(this.firstNumber, this.secondNumber);
        return this.result;
    }
    
    /**
     * Finds the minimum of the two numbers
     * @return minimum value
     */
    public double findMinimum() {
        this.operation = "Minimum";
        this.result = Math.min(this.firstNumber, this.secondNumber);
        return this.result;
    }
    
    /**
     * Calculates the average of the two numbers
     * @return average value
     */
    public double calculateAverage() {
        this.operation = "Average";
        this.result = (this.firstNumber + this.secondNumber) / 2.0;
        return this.result;
    }
    
    /**
     * Gets the first number
     * @return first number
     */
    public double getFirstNumber() {
        return this.firstNumber;
    }
    
    /**
     * Gets the second number
     * @return second number
     */
    public double getSecondNumber() {
        return this.secondNumber;
    }
    
    /**
     * Gets the last operation performed
     * @return operation name
     */
    public String getOperation() {
        return this.operation;
    }
    
    /**
     * Gets the last result
     * @return calculation result
     */
    public double getResult() {
        return this.result;
    }
    
    /**
     * Displays the calculation summary
     */
    public void displayCalculationSummary() {
        System.out.println("\n=== CALCULATION SUMMARY ===");
        System.out.println("First Number: " + this.firstNumber);
        if (!this.operation.equals("Square Root") && !this.operation.equals("Factorial")) {
            System.out.println("Second Number: " + this.secondNumber);
        }
        System.out.println("Operation: " + this.operation);
        System.out.println("Result: " + String.format("%.4f", this.result));
        System.out.println("===========================");
    }
    
    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            MathOperationsApp mathApp = new MathOperationsApp();
            
            System.out.println("Welcome to Mathematical Operations App!");
            System.out.println("======================================");
            
            int choice;
            do {
                System.out.println("\n=== MATHEMATICAL OPERATIONS MENU ===");
                System.out.println("1.  Addition");
                System.out.println("2.  Subtraction");
                System.out.println("3.  Multiplication");
                System.out.println("4.  Division");
                System.out.println("5.  Power");
                System.out.println("6.  Square Root");
                System.out.println("7.  Factorial");
                System.out.println("8.  Maximum");
                System.out.println("9.  Minimum");
                System.out.println("10. Average");
                System.out.println("11. Display Last Result");
                System.out.println("12. Exit");
                System.out.println("=====================================");
                System.out.print("Enter your choice (1-12): ");
                
                choice = scanner.nextInt();
                
                switch (choice) {
                    case 1: case 2: case 3: case 4: case 5: case 8: case 9: case 10:
                        System.out.print("Enter first number: ");
                        double num1 = scanner.nextDouble();
                        System.out.print("Enter second number: ");
                        double num2 = scanner.nextDouble();
                        mathApp.setNumbers(num1, num2);
                        
                        try {
                            switch (choice) {
                                case 1:
                                    System.out.println("Result: " + mathApp.performAddition());
                                    break;
                                case 2:
                                    System.out.println("Result: " + mathApp.performSubtraction());
                                    break;
                                case 3:
                                    System.out.println("Result: " + mathApp.performMultiplication());
                                    break;
                                case 4:
                                    System.out.println("Result: " + mathApp.performDivision());
                                    break;
                                case 5:
                                    System.out.println("Result: " + mathApp.performPower());
                                    break;
                                case 8:
                                    System.out.println("Maximum: " + mathApp.findMaximum());
                                    break;
                                case 9:
                                    System.out.println("Minimum: " + mathApp.findMinimum());
                                    break;
                                case 10:
                                    System.out.println("Average: " + mathApp.calculateAverage());
                                    break;
                            }
                            mathApp.displayCalculationSummary();
                        } catch (ArithmeticException e) {
                            System.err.println("Error: " + e.getMessage());
                        }
                        break;
                        
                    case 6: case 7:
                        System.out.print("Enter number: ");
                        double number = scanner.nextDouble();
                        mathApp.setNumbers(number, 0);
                        
                        try {
                            if (choice == 6) {
                                System.out.println("Square Root: " + mathApp.performSquareRoot());
                            } else {
                                System.out.println("Factorial: " + mathApp.performFactorial());
                            }
                            mathApp.displayCalculationSummary();
                        } catch (ArithmeticException e) {
                            System.err.println("Error: " + e.getMessage());
                        }
                        break;
                        
                    case 11:
                        if (mathApp.getOperation().isEmpty()) {
                            System.out.println("No calculations performed yet.");
                        } else {
                            mathApp.displayCalculationSummary();
                        }
                        break;
                        
                    case 12:
                        System.out.println("Thank you for using Mathematical Operations App!");
                        break;
                        
                    default:
                        System.out.println("Invalid choice! Please enter 1-12.");
                }
                
            } while (choice != 12);
            
        } catch (Exception e) {
            System.err.println("Error: Invalid input. Please enter valid numbers.");
        }
    }
}
