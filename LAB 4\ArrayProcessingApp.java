import java.util.Scanner;
import java.util.Arrays;

/**
 * ArrayProcessingApp - Array manipulation and processing
 * CSC 104 Programming Activity #4
 * Demonstrates arrays, loops, and array algorithms
 */
public class ArrayProcessingApp {
    // Instance variables
    private int[] numbers;
    private int size;
    
    /**
     * Constructor with array size
     * @param size size of the array
     */
    public ArrayProcessingApp(int size) {
        this.size = size;
        this.numbers = new int[size];
    }
    
    /**
     * Fills the array with user input
     * @param scanner Scanner object for input
     */
    public void fillArray(Scanner scanner) {
        System.out.println("Enter " + size + " numbers:");
        for (int i = 0; i < size; i++) {
            System.out.print("Number " + (i + 1) + ": ");
            numbers[i] = scanner.nextInt();
        }
    }
    
    /**
     * Displays the array
     */
    public void displayArray() {
        System.out.println("Array contents: " + Arrays.toString(numbers));
    }
    
    /**
     * Finds the sum of all elements
     * @return sum of array elements
     */
    public int findSum() {
        int sum = 0;
        for (int num : numbers) {
            sum += num;
        }
        return sum;
    }
    
    /**
     * Finds the average of all elements
     * @return average of array elements
     */
    public double findAverage() {
        return (double) findSum() / size;
    }
    
    /**
     * Finds the maximum element
     * @return maximum element in array
     */
    public int findMax() {
        int max = numbers[0];
        for (int i = 1; i < size; i++) {
            if (numbers[i] > max) {
                max = numbers[i];
            }
        }
        return max;
    }
    
    /**
     * Finds the minimum element
     * @return minimum element in array
     */
    public int findMin() {
        int min = numbers[0];
        for (int i = 1; i < size; i++) {
            if (numbers[i] < min) {
                min = numbers[i];
            }
        }
        return min;
    }
    
    /**
     * Searches for a specific value
     * @param target value to search for
     * @return index of the value, -1 if not found
     */
    public int linearSearch(int target) {
        for (int i = 0; i < size; i++) {
            if (numbers[i] == target) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * Counts occurrences of a specific value
     * @param target value to count
     * @return number of occurrences
     */
    public int countOccurrences(int target) {
        int count = 0;
        for (int num : numbers) {
            if (num == target) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * Sorts the array in ascending order (bubble sort)
     */
    public void sortArray() {
        for (int i = 0; i < size - 1; i++) {
            for (int j = 0; j < size - i - 1; j++) {
                if (numbers[j] > numbers[j + 1]) {
                    // Swap elements
                    int temp = numbers[j];
                    numbers[j] = numbers[j + 1];
                    numbers[j + 1] = temp;
                }
            }
        }
    }
    
    /**
     * Reverses the array
     */
    public void reverseArray() {
        for (int i = 0; i < size / 2; i++) {
            int temp = numbers[i];
            numbers[i] = numbers[size - 1 - i];
            numbers[size - 1 - i] = temp;
        }
    }
    
    /**
     * Counts even and odd numbers
     * @return array with [evenCount, oddCount]
     */
    public int[] countEvenOdd() {
        int evenCount = 0, oddCount = 0;
        for (int num : numbers) {
            if (num % 2 == 0) {
                evenCount++;
            } else {
                oddCount++;
            }
        }
        return new int[]{evenCount, oddCount};
    }
    
    /**
     * Displays array statistics
     */
    public void displayStatistics() {
        System.out.println("\n=== ARRAY STATISTICS ===");
        System.out.println("Array size: " + size);
        System.out.println("Sum: " + findSum());
        System.out.println("Average: " + String.format("%.2f", findAverage()));
        System.out.println("Maximum: " + findMax());
        System.out.println("Minimum: " + findMin());
        
        int[] evenOdd = countEvenOdd();
        System.out.println("Even numbers: " + evenOdd[0]);
        System.out.println("Odd numbers: " + evenOdd[1]);
        System.out.println("========================");
    }
    
    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            System.out.println("Welcome to Array Processing App!");
            System.out.print("Enter the size of the array: ");
            int size = scanner.nextInt();
            
            if (size <= 0) {
                System.out.println("Array size must be positive!");
                return;
            }
            
            ArrayProcessingApp app = new ArrayProcessingApp(size);
            
            // Fill array with user input
            app.fillArray(scanner);
            
            int choice;
            do {
                System.out.println("\n=== ARRAY OPERATIONS MENU ===");
                System.out.println("1. Display Array");
                System.out.println("2. Show Statistics");
                System.out.println("3. Search for Value");
                System.out.println("4. Count Occurrences");
                System.out.println("5. Sort Array");
                System.out.println("6. Reverse Array");
                System.out.println("7. Exit");
                System.out.println("==============================");
                System.out.print("Enter your choice (1-7): ");
                choice = scanner.nextInt();
                
                switch (choice) {
                    case 1:
                        app.displayArray();
                        break;
                        
                    case 2:
                        app.displayStatistics();
                        break;
                        
                    case 3:
                        System.out.print("Enter value to search: ");
                        int searchValue = scanner.nextInt();
                        int index = app.linearSearch(searchValue);
                        if (index != -1) {
                            System.out.println("Value " + searchValue + " found at index " + index);
                        } else {
                            System.out.println("Value " + searchValue + " not found in array");
                        }
                        break;
                        
                    case 4:
                        System.out.print("Enter value to count: ");
                        int countValue = scanner.nextInt();
                        int count = app.countOccurrences(countValue);
                        System.out.println("Value " + countValue + " appears " + count + " time(s)");
                        break;
                        
                    case 5:
                        app.sortArray();
                        System.out.println("Array sorted in ascending order!");
                        app.displayArray();
                        break;
                        
                    case 6:
                        app.reverseArray();
                        System.out.println("Array reversed!");
                        app.displayArray();
                        break;
                        
                    case 7:
                        System.out.println("Thank you for using Array Processing App!");
                        break;
                        
                    default:
                        System.out.println("Invalid choice! Please enter 1-7.");
                }
                
            } while (choice != 7);
            
        } catch (Exception e) {
            System.err.println("Error: Invalid input. Please enter valid integers.");
        }
    }
}
