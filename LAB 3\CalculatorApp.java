import java.util.Scanner;

/**
 * CalculatorApp - Basic arithmetic calculator
 * CSC 104 Programming Activity #3
 * Demonstrates methods, parameters, and return values
 */
public class CalculatorApp {
    // Instance variables
    private double result;
    private String lastOperation;
    
    /**
     * Default constructor
     */
    public CalculatorApp() {
        this.result = 0.0;
        this.lastOperation = "None";
    }
    
    /**
     * Addition method
     * @param a first number
     * @param b second number
     * @return sum of a and b
     */
    public double add(double a, double b) {
        this.result = a + b;
        this.lastOperation = "Addition";
        return this.result;
    }
    
    /**
     * Subtraction method
     * @param a first number
     * @param b second number
     * @return difference of a and b
     */
    public double subtract(double a, double b) {
        this.result = a - b;
        this.lastOperation = "Subtraction";
        return this.result;
    }
    
    /**
     * Multiplication method
     * @param a first number
     * @param b second number
     * @return product of a and b
     */
    public double multiply(double a, double b) {
        this.result = a * b;
        this.lastOperation = "Multiplication";
        return this.result;
    }
    
    /**
     * Division method
     * @param a dividend
     * @param b divisor
     * @return quotient of a divided by b
     * @throws ArithmeticException if divisor is zero
     */
    public double divide(double a, double b) throws ArithmeticException {
        if (b == 0) {
            throw new ArithmeticException("Cannot divide by zero!");
        }
        this.result = a / b;
        this.lastOperation = "Division";
        return this.result;
    }
    
    /**
     * Power method
     * @param base base number
     * @param exponent exponent
     * @return base raised to the power of exponent
     */
    public double power(double base, double exponent) {
        this.result = Math.pow(base, exponent);
        this.lastOperation = "Power";
        return this.result;
    }
    
    /**
     * Square root method
     * @param number number to find square root of
     * @return square root of the number
     * @throws ArithmeticException if number is negative
     */
    public double squareRoot(double number) throws ArithmeticException {
        if (number < 0) {
            throw new ArithmeticException("Cannot find square root of negative number!");
        }
        this.result = Math.sqrt(number);
        this.lastOperation = "Square Root";
        return this.result;
    }
    
    /**
     * Gets the last result
     * @return the last calculated result
     */
    public double getResult() {
        return this.result;
    }
    
    /**
     * Gets the last operation performed
     * @return the last operation as string
     */
    public String getLastOperation() {
        return this.lastOperation;
    }
    
    /**
     * Clears the calculator (resets result and operation)
     */
    public void clear() {
        this.result = 0.0;
        this.lastOperation = "None";
    }
    
    /**
     * Displays the calculator menu
     */
    public void displayMenu() {
        System.out.println("\n=== CALCULATOR MENU ===");
        System.out.println("1. Addition");
        System.out.println("2. Subtraction");
        System.out.println("3. Multiplication");
        System.out.println("4. Division");
        System.out.println("5. Power");
        System.out.println("6. Square Root");
        System.out.println("7. Clear");
        System.out.println("8. Exit");
        System.out.println("=======================");
    }
    
    public static void main(String[] args) {
        try (Scanner scanner = new Scanner(System.in)) {
            CalculatorApp calc = new CalculatorApp();
            int choice;
            
            System.out.println("Welcome to the Calculator App!");
            
            do {
                calc.displayMenu();
                System.out.print("Enter your choice (1-8): ");
                choice = scanner.nextInt();
                
                switch (choice) {
                    case 1: // Addition
                        System.out.print("Enter first number: ");
                        double a1 = scanner.nextDouble();
                        System.out.print("Enter second number: ");
                        double b1 = scanner.nextDouble();
                        System.out.println("Result: " + calc.add(a1, b1));
                        break;
                        
                    case 2: // Subtraction
                        System.out.print("Enter first number: ");
                        double a2 = scanner.nextDouble();
                        System.out.print("Enter second number: ");
                        double b2 = scanner.nextDouble();
                        System.out.println("Result: " + calc.subtract(a2, b2));
                        break;
                        
                    case 3: // Multiplication
                        System.out.print("Enter first number: ");
                        double a3 = scanner.nextDouble();
                        System.out.print("Enter second number: ");
                        double b3 = scanner.nextDouble();
                        System.out.println("Result: " + calc.multiply(a3, b3));
                        break;
                        
                    case 4: // Division
                        try {
                            System.out.print("Enter dividend: ");
                            double a4 = scanner.nextDouble();
                            System.out.print("Enter divisor: ");
                            double b4 = scanner.nextDouble();
                            System.out.println("Result: " + calc.divide(a4, b4));
                        } catch (ArithmeticException e) {
                            System.err.println("Error: " + e.getMessage());
                        }
                        break;
                        
                    case 5: // Power
                        System.out.print("Enter base: ");
                        double base = scanner.nextDouble();
                        System.out.print("Enter exponent: ");
                        double exp = scanner.nextDouble();
                        System.out.println("Result: " + calc.power(base, exp));
                        break;
                        
                    case 6: // Square Root
                        try {
                            System.out.print("Enter number: ");
                            double num = scanner.nextDouble();
                            System.out.println("Result: " + calc.squareRoot(num));
                        } catch (ArithmeticException e) {
                            System.err.println("Error: " + e.getMessage());
                        }
                        break;
                        
                    case 7: // Clear
                        calc.clear();
                        System.out.println("Calculator cleared!");
                        break;
                        
                    case 8: // Exit
                        System.out.println("Thank you for using Calculator App!");
                        break;
                        
                    default:
                        System.out.println("Invalid choice! Please enter 1-8.");
                }
                
                if (choice >= 1 && choice <= 6) {
                    System.out.println("Last Operation: " + calc.getLastOperation());
                    System.out.println("Current Result: " + calc.getResult());
                }
                
            } while (choice != 8);
            
        } catch (Exception e) {
            System.err.println("Error: Invalid input. Please enter valid numbers.");
        }
    }
}
